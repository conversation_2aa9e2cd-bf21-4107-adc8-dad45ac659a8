/**
 * Quick test to verify that the bestWords function consolidation was successful
 */

import { Board } from './src/lib/models/Board';
import { bestWords } from './src/lib/bestWords';

function testBestWordsConsolidation() {
	console.log('🧪 Testing bestWords function consolidation...\n');

	try {
		// Create a test board
		const board = Board.createRandom();
		console.log('✅ Board created successfully');

		// Test bestWords function
		const words = bestWords(board, 10);
		console.log(`✅ bestWords function executed successfully`);
		console.log(`   Found ${words.length} words`);

		// Verify results structure
		if (Array.isArray(words)) {
			console.log('✅ Function returns an array');
		} else {
			throw new Error('Function should return an array');
		}

		// Check first word if any exist
		if (words.length > 0) {
			const firstWord = words[0];
			if (firstWord.letters && typeof firstWord.score === 'number' && Array.isArray(firstWord.positions)) {
				console.log(`✅ Word structure is correct: "${firstWord.letters}" (${firstWord.score} points)`);
			} else {
				throw new Error('Word structure is invalid');
			}
		}

		// Test edge cases
		const emptyResult = bestWords(board, 0);
		if (emptyResult.length === 0) {
			console.log('✅ K=0 returns empty array');
		} else {
			throw new Error('K=0 should return empty array');
		}

		console.log('\n🎉 All tests passed! bestWords function consolidation was successful.');
		return true;

	} catch (error) {
		console.error('❌ Test failed:', error);
		return false;
	}
}

// Run the test
testBestWordsConsolidation();

export { testBestWordsConsolidation };
